import React from "react";
import ReactDOM from "react-dom/client";
import {
  Alert,
  AlertDescription,
  AlertTitle,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Badge,
  Button,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Checkbox,
  Header,
  HeaderBrand,
  HeaderNavLink,
  Input,
  Label,
  Progress,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
  Skeleton,
  Slider,
  Switch,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Textarea,
  ThemeProvider,
  ThemeToggle,
  Toggle,
  useTheme,
} from "@nui/ui";
import {
  Bell,
  Heart,
  Mail,
  Search,
  Settings,
  Star,
  User,
  AlertCircle,
  CheckCircle,
  Info,
  AlertTriangle,
} from "lucide-react";

function App() {
  const {
    config: { mode, variant },
    isDark,
    availableVariants,
    setMode,
    setVariant,
    toggleMode,
    cycleVariant,
  } = useTheme();

  return (
    <>
      <div className="min-h-screen bg-background text-foreground">
        <Header
          title={
            <HeaderBrand>
              nui
              <Badge variant="success" className="ml-2">
                Live
              </Badge>
            </HeaderBrand>
          }
          navigation={
            <>
              <HeaderNavLink href="#" active>
                Demo
              </HeaderNavLink>
              <HeaderNavLink href="#">Components</HeaderNavLink>
              <HeaderNavLink href="#">Docs</HeaderNavLink>
            </>
          }
          actions={
            <div className="flex items-center gap-2">
              <Badge variant="outline">Admin</Badge>
              <Avatar size="sm">
                <AvatarImage
                  src="https://github.com/shadcn.png"
                  alt="User Avatar"
                />
                <AvatarFallback>CN</AvatarFallback>
              </Avatar>
            </div>
          }
          themeToggleProps={{
            showLabels: false,
          }}
          sticky
          bordered
        />

        <div className="max-w-6xl mx-auto p-8 space-y-8">
          <div className="text-center space-y-2">
            <h1 className="text-4xl font-bold">nui Component Library</h1>
            <p className="text-muted-foreground text-lg">
              Explore our comprehensive collection of UI components with live
              theme switching
            </p>
          </div>

          {/* Current Theme Status Bar */}
          <div className="flex items-center justify-center gap-4 p-4 bg-card/50 border rounded-lg">
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">Mode:</span>
              <Badge variant={isDark ? "secondary" : "default"}>{mode}</Badge>
            </div>
            <Separator orientation="vertical" className="h-4" />
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">Variant:</span>
              <Badge variant="outline">{variant}</Badge>
            </div>
            <Separator orientation="vertical" className="h-4" />
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">Available:</span>
              <code className="text-xs bg-muted px-2 py-1 rounded">
                {availableVariants.join(", ")}
              </code>
            </div>
          </div>

          <Tabs defaultValue="theme" className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="theme">Theme</TabsTrigger>
              <TabsTrigger value="buttons">Buttons</TabsTrigger>
              <TabsTrigger value="forms">Forms</TabsTrigger>
              <TabsTrigger value="feedback">Feedback</TabsTrigger>
              <TabsTrigger value="data">Data</TabsTrigger>
              <TabsTrigger value="layout">Layout</TabsTrigger>
            </TabsList>

            <TabsContent value="theme" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Theme System</CardTitle>
                  <CardDescription>
                    Control the appearance and behavior of all components
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="space-y-4">
                      <Label className="text-sm font-medium">
                        Theme Controls
                      </Label>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="dark-mode-switch">Dark Mode</Label>
                          <Switch
                            id="dark-mode-switch"
                            checked={isDark}
                            onCheckedChange={toggleMode}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="variant-select">Theme Variant</Label>
                          <Select
                            value={variant}
                            onValueChange={(value) =>
                              setVariant(value as string)
                            }
                          >
                            <SelectTrigger
                              id="variant-select"
                              className="w-full"
                            >
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {availableVariants.map((v) => (
                                <SelectItem key={v} value={v}>
                                  {v.charAt(0).toUpperCase() + v.slice(1)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <Label className="text-sm font-medium">
                        Quick Actions
                      </Label>

                      <div className="space-y-2">
                        <Button onClick={toggleMode} className="w-full">
                          Toggle Mode ({mode})
                        </Button>
                        <Button
                          onClick={() => cycleVariant(availableVariants)}
                          className="w-full"
                          variant="secondary"
                        >
                          Cycle Variant ({variant})
                        </Button>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-xs font-medium text-muted-foreground">
                          MODE BUTTONS
                        </Label>
                        <div className="grid grid-cols-3 gap-1">
                          {(["light", "dark", "auto"] as const).map((m) => (
                            <Button
                              key={m}
                              onClick={() => setMode(m)}
                              variant={mode === m ? "default" : "outline"}
                              size="sm"
                            >
                              {m}
                            </Button>
                          ))}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-xs font-medium text-muted-foreground">
                          VARIANT BUTTONS
                        </Label>
                        <div className="grid grid-cols-2 gap-1">
                          {availableVariants.map((v) => (
                            <Button
                              key={v}
                              onClick={() => setVariant(v)}
                              variant={variant === v ? "default" : "outline"}
                              size="sm"
                            >
                              {v}
                            </Button>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <Label className="text-sm font-medium">
                        Theme Toggles
                      </Label>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Mode Only</span>
                          <ThemeToggle modeOnly />
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm">Variant Only</span>
                          <ThemeToggle variantOnly />
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm">Combined</span>
                          <ThemeToggle />
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm">With Labels</span>
                          <ThemeToggle showLabels />
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm">Outline Style</span>
                          <ThemeToggle variant="outline" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Live Preview</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Sample Card</CardTitle>
                          <CardDescription>
                            This card updates with your theme changes
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div className="flex gap-2">
                            <Button size="sm">Primary</Button>
                            <Button size="sm" variant="secondary">
                              Secondary
                            </Button>
                            <Button size="sm" variant="outline">
                              Outline
                            </Button>
                          </div>
                          <div className="flex gap-2">
                            <Badge variant="success">Success</Badge>
                            <Badge variant="warning">Warning</Badge>
                            <Badge variant="info">Info</Badge>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle>Form Elements</CardTitle>
                          <CardDescription>
                            See how form controls adapt to themes
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <Input placeholder="Sample input" />
                          <div className="flex items-center space-x-2">
                            <Checkbox id="sample-checkbox" />
                            <Label htmlFor="sample-checkbox">
                              Sample checkbox
                            </Label>
                          </div>
                          <div className="flex items-center justify-between">
                            <Label>Sample switch</Label>
                            <Switch />
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="buttons" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Buttons & Actions</CardTitle>
                  <CardDescription>
                    Interactive elements for user actions
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">
                      Button Variants
                    </Label>
                    <div className="flex flex-wrap gap-2">
                      <Button variant="default">Default</Button>
                      <Button variant="secondary">Secondary</Button>
                      <Button variant="outline">Outline</Button>
                      <Button variant="ghost">Ghost</Button>
                      <Button variant="destructive">Destructive</Button>
                      <Button variant="link">Link</Button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Button Sizes</Label>
                    <div className="flex flex-wrap items-center gap-2">
                      <Button size="sm">Small</Button>
                      <Button size="md">Medium</Button>
                      <Button size="lg">Large</Button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Icon Buttons</Label>
                    <div className="flex flex-wrap gap-2">
                      <Button size="icon-sm">
                        <Heart className="size-3" />
                      </Button>
                      <Button size="icon">
                        <Star className="size-4" />
                      </Button>
                      <Button size="icon-lg">
                        <Settings className="size-5" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">
                      Toggle Buttons
                    </Label>
                    <div className="flex flex-wrap gap-2">
                      <Toggle>
                        <Bell className="size-4" />
                        Notifications
                      </Toggle>
                      <Toggle variant="outline">
                        <Mail className="size-4" />
                        Messages
                      </Toggle>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="forms" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Form Controls</CardTitle>
                  <CardDescription>
                    Input elements for collecting user data
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <Label htmlFor="name">Name</Label>
                      <Input
                        id="name"
                        placeholder="Enter your name"
                        leadingIcon={<User />}
                      />
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="Enter your email"
                        leadingIcon={<Mail />}
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label htmlFor="search">Search</Label>
                    <Input
                      id="search"
                      placeholder="Search components..."
                      leadingIcon={<Search />}
                    />
                  </div>

                  <div className="space-y-3">
                    <Label htmlFor="message">Message</Label>
                    <Textarea
                      id="message"
                      placeholder="Type your message here..."
                      rows={3}
                    />
                  </div>

                  <div className="space-y-3">
                    <Label>Priority Level</Label>
                    <Select defaultValue="medium">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <Label>Preferences</Label>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="notifications" />
                          <Label htmlFor="notifications">
                            Email notifications
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="marketing" />
                          <Label htmlFor="marketing">Marketing emails</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="updates" defaultChecked />
                          <Label htmlFor="updates">Product updates</Label>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label>Account Type</Label>
                      <RadioGroup defaultValue="personal">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="personal" id="personal" />
                          <Label htmlFor="personal">Personal</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="business" id="business" />
                          <Label htmlFor="business">Business</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="enterprise" id="enterprise" />
                          <Label htmlFor="enterprise">Enterprise</Label>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label>Volume</Label>
                      <span className="text-sm text-muted-foreground">75%</span>
                    </div>
                    <Slider defaultValue={[75]} max={100} step={1} />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="dark-mode">Dark Mode</Label>
                    <Switch id="dark-mode" />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="feedback" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Feedback & Status</CardTitle>
                  <CardDescription>
                    Components for displaying status and feedback
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Badges</Label>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="default">Default</Badge>
                      <Badge variant="secondary">Secondary</Badge>
                      <Badge variant="outline">Outline</Badge>
                      <Badge variant="success">Success</Badge>
                      <Badge variant="warning">Warning</Badge>
                      <Badge variant="info">Info</Badge>
                      <Badge variant="danger">Danger</Badge>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Alerts</Label>
                    <div className="space-y-3">
                      <Alert>
                        <Info className="size-4" />
                        <AlertTitle>Information</AlertTitle>
                        <AlertDescription>
                          This is an informational alert with some details.
                        </AlertDescription>
                      </Alert>

                      <Alert variant="success">
                        <CheckCircle className="size-4" />
                        <AlertTitle>Success</AlertTitle>
                        <AlertDescription>
                          Your changes have been saved successfully.
                        </AlertDescription>
                      </Alert>

                      <Alert variant="warning">
                        <AlertTriangle className="size-4" />
                        <AlertTitle>Warning</AlertTitle>
                        <AlertDescription>
                          Please review your settings before proceeding.
                        </AlertDescription>
                      </Alert>

                      <Alert variant="danger">
                        <AlertCircle className="size-4" />
                        <AlertTitle>Error</AlertTitle>
                        <AlertDescription>
                          Something went wrong. Please try again.
                        </AlertDescription>
                      </Alert>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Progress</Label>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Upload Progress</span>
                          <span>45%</span>
                        </div>
                        <Progress value={45} />
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Installation</span>
                          <span>78%</span>
                        </div>
                        <Progress value={78} />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">
                      Loading States
                    </Label>
                    <div className="space-y-3">
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="data" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Data Display</CardTitle>
                  <CardDescription>
                    Components for displaying and organizing data
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Avatars</Label>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Avatar size="sm">
                          <AvatarImage
                            src="https://github.com/shadcn.png"
                            alt="User"
                          />
                          <AvatarFallback>CN</AvatarFallback>
                        </Avatar>
                        <span className="text-sm">Small</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Avatar size="md">
                          <AvatarImage
                            src="https://github.com/vercel.png"
                            alt="Vercel"
                          />
                          <AvatarFallback>V</AvatarFallback>
                        </Avatar>
                        <span className="text-sm">Medium</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Avatar size="lg">
                          <AvatarFallback>LG</AvatarFallback>
                        </Avatar>
                        <span className="text-sm">Large</span>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">User Profiles</Label>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Avatar size="sm">
                            <AvatarImage
                              src="https://github.com/shadcn.png"
                              alt="John Doe"
                            />
                            <AvatarFallback>JD</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">John Doe</div>
                            <div className="text-sm text-muted-foreground">
                              <EMAIL>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="success">Active</Badge>
                          <Button size="sm" variant="outline">
                            View
                          </Button>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Avatar size="sm">
                            <AvatarImage
                              src="https://github.com/vercel.png"
                              alt="Jane Smith"
                            />
                            <AvatarFallback>JS</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">Jane Smith</div>
                            <div className="text-sm text-muted-foreground">
                              <EMAIL>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="warning">Pending</Badge>
                          <Button size="sm" variant="outline">
                            View
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">
                      Statistics Cards
                    </Label>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Total Users
                              </p>
                              <p className="text-2xl font-bold">1,234</p>
                            </div>
                            <User className="size-8 text-muted-foreground" />
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Messages
                              </p>
                              <p className="text-2xl font-bold">567</p>
                            </div>
                            <Mail className="size-8 text-muted-foreground" />
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Notifications
                              </p>
                              <p className="text-2xl font-bold">89</p>
                            </div>
                            <Bell className="size-8 text-muted-foreground" />
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="layout" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Layout & Structure</CardTitle>
                  <CardDescription>
                    Components for organizing content and layout
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Cards</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Project Alpha</CardTitle>
                          <CardDescription>
                            A revolutionary new project
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground">
                            This project aims to transform how we think about
                            user interfaces.
                          </p>
                        </CardContent>
                        <CardFooter>
                          <Button className="w-full">Learn More</Button>
                        </CardFooter>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle>Analytics Dashboard</CardTitle>
                          <CardDescription>
                            Real-time insights and metrics
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>Conversion Rate</span>
                              <span className="font-medium">12.5%</span>
                            </div>
                            <Progress value={12.5} />
                          </div>
                        </CardContent>
                        <CardFooter>
                          <Button variant="outline" className="w-full">
                            View Details
                          </Button>
                        </CardFooter>
                      </Card>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">
                      Content Organization
                    </Label>
                    <Card>
                      <CardContent className="p-6">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <h3 className="font-medium">Team Settings</h3>
                            <Badge variant="info">New</Badge>
                          </div>

                          <Separator />

                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">
                                  Team Notifications
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  Get notified about team activities
                                </p>
                              </div>
                              <Switch defaultChecked />
                            </div>

                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">Auto-save</p>
                                <p className="text-sm text-muted-foreground">
                                  Automatically save your work
                                </p>
                              </div>
                              <Switch />
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </>
  );
}

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ThemeProvider>
      <App />
    </ThemeProvider>
  </React.StrictMode>
);
