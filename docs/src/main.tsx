import React from "react";
import ReactDOM from "react-dom/client";
import {
  Alert,
  AlertDescription,
  AlertTitle,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Badge,
  Button,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Checkbox,
  Header,
  HeaderBrand,
  HeaderNavLink,
  Input,
  Label,
  Progress,
  ProgressValue,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
  Skeleton,
  Slider,
  SliderValue,
  Switch,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Textarea,
  ThemeProvider,
  ThemeToggle,
  Toggle,
  useTheme,
} from "@nui/ui";
import {
  Bell,
  Heart,
  Mail,
  Search,
  Settings,
  Star,
  User,
  AlertCircle,
  CheckCircle,
  Info,
  AlertTriangle,
} from "lucide-react";

type ThemeMode = "light" | "dark" | "auto";

function App() {
  const {
    config: { mode, variant },
    isDark,
    availableVariants,
    setMode,
    setVariant,
    toggleMode,
    cycleVariant,
  } = useTheme();

  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header
        title={
          <HeaderBrand>
            nui
            <Badge variant="success" className="ml-2">
              Live
            </Badge>
          </HeaderBrand>
        }
        navigation={
          <>
            <HeaderNavLink href="#" active>
              Demo
            </HeaderNavLink>
            <HeaderNavLink href="#">Components</HeaderNavLink>
            <HeaderNavLink href="#">Docs</HeaderNavLink>
          </>
        }
        actions={
          <div className="flex items-center gap-2">
            <Badge variant="outline">Admin</Badge>
            <Avatar size="sm">
              <AvatarImage
                src="https://github.com/shadcn.png"
                alt="User Avatar"
              />
              <AvatarFallback>CN</AvatarFallback>
            </Avatar>
          </div>
        }
        themeToggleProps={{
          showLabels: false,
        }}
        sticky
        bordered
      />

      <div className="max-w-4xl mx-auto p-8 space-y-6">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">Theme System Demo</h1>
          <p className="text-muted-foreground">
            Explore the theme system with mode and variant switching
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 bg-card border rounded-lg space-y-2 text-sm">
            <h2 className="text-xl font-semibold mb-3">Current Theme</h2>
            <div className="flex items-center gap-2">
              Mode:{" "}
              <Badge variant={isDark ? "secondary" : "default"}>{mode}</Badge>
            </div>
            <div className="flex items-center gap-2">
              Variant: <Badge variant="outline">{variant}</Badge>
            </div>
            <div className="flex items-center gap-2">
              Is Dark:{" "}
              <Badge variant={isDark ? "info" : "warning"}>
                {isDark.toString()}
              </Badge>
            </div>
            <div>
              Available: <code>{availableVariants.join(", ")}</code>
            </div>
          </div>

          <div className="p-4 bg-card border rounded-lg space-y-3">
            <h2 className="text-xl font-semibold mb-3">Theme Controls</h2>

            <div className="flex items-center justify-between">
              <Label htmlFor="dark-mode-switch">Dark Mode</Label>
              <Switch
                id="dark-mode-switch"
                checked={isDark}
                onCheckedChange={toggleMode}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="variant-select">Theme Variant</Label>
              <Select
                value={variant}
                onValueChange={(value) => setVariant(value as string)}
              >
                <SelectTrigger id="variant-select" className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {availableVariants.map((v) => (
                    <SelectItem key={v} value={v}>
                      {v.charAt(0).toUpperCase() + v.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Button onClick={toggleMode} className="w-full">
              Toggle Mode ({mode})
            </Button>
            <Button
              onClick={() => cycleVariant(availableVariants)}
              className="w-full"
              variant="secondary"
            >
              Cycle Variant ({variant})
            </Button>

            <div className="grid grid-cols-3 gap-2">
              {(["light", "dark", "auto"] as ThemeMode[]).map((m) => (
                <Button
                  key={m}
                  onClick={() => setMode(m)}
                  variant={mode === m ? "default" : "outline"}
                  size="sm"
                >
                  {m}
                </Button>
              ))}
            </div>

            <div className="grid grid-cols-2 gap-2">
              {availableVariants.map((v) => (
                <Button
                  key={v}
                  onClick={() => setVariant(v)}
                  variant={variant === v ? "default" : "outline"}
                  size="sm"
                >
                  {v}
                </Button>
              ))}
            </div>
          </div>

          <div className="p-4 bg-card border rounded-lg space-y-3">
            <h2 className="text-xl font-semibold mb-3">Theme Toggles</h2>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Mode Only</span>
                <ThemeToggle modeOnly />
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Variant Only</span>
                <ThemeToggle variantOnly />
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Combined</span>
                <ThemeToggle />
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">With Labels</span>
                <ThemeToggle showLabels />
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Outline Style</span>
                <ThemeToggle variant="outline" />
              </div>
            </div>
          </div>
        </div>

        {/* Component Showcase */}
        <div className="space-y-8">
          <div className="text-center space-y-2">
            <h2 className="text-2xl font-bold">Component Showcase</h2>
            <p className="text-muted-foreground">
              Explore our comprehensive collection of UI components
            </p>
          </div>

          <Tabs defaultValue="buttons" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="buttons">Buttons</TabsTrigger>
              <TabsTrigger value="forms">Forms</TabsTrigger>
              <TabsTrigger value="feedback">Feedback</TabsTrigger>
              <TabsTrigger value="data">Data</TabsTrigger>
              <TabsTrigger value="layout">Layout</TabsTrigger>
            </TabsList>

            <TabsContent value="buttons" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Buttons & Actions</CardTitle>
                  <CardDescription>
                    Interactive elements for user actions
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">
                      Button Variants
                    </Label>
                    <div className="flex flex-wrap gap-2">
                      <Button variant="default">Default</Button>
                      <Button variant="secondary">Secondary</Button>
                      <Button variant="outline">Outline</Button>
                      <Button variant="ghost">Ghost</Button>
                      <Button variant="destructive">Destructive</Button>
                      <Button variant="link">Link</Button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Button Sizes</Label>
                    <div className="flex flex-wrap items-center gap-2">
                      <Button size="sm">Small</Button>
                      <Button size="md">Medium</Button>
                      <Button size="lg">Large</Button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Icon Buttons</Label>
                    <div className="flex flex-wrap gap-2">
                      <Button size="icon-sm">
                        <Heart className="size-3" />
                      </Button>
                      <Button size="icon">
                        <Star className="size-4" />
                      </Button>
                      <Button size="icon-lg">
                        <Settings className="size-5" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">
                      Toggle Buttons
                    </Label>
                    <div className="flex flex-wrap gap-2">
                      <Toggle>
                        <Bell className="size-4" />
                        Notifications
                      </Toggle>
                      <Toggle variant="outline">
                        <Mail className="size-4" />
                        Messages
                      </Toggle>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="forms" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Form Controls</CardTitle>
                  <CardDescription>
                    Input elements for collecting user data
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <Label htmlFor="name">Name</Label>
                      <Input
                        id="name"
                        placeholder="Enter your name"
                        leadingIcon={<User />}
                      />
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="Enter your email"
                        leadingIcon={<Mail />}
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label htmlFor="search">Search</Label>
                    <Input
                      id="search"
                      placeholder="Search components..."
                      leadingIcon={<Search />}
                    />
                  </div>

                  <div className="space-y-3">
                    <Label htmlFor="message">Message</Label>
                    <Textarea
                      id="message"
                      placeholder="Type your message here..."
                      rows={3}
                    />
                  </div>

                  <div className="space-y-3">
                    <Label>Priority Level</Label>
                    <Select defaultValue="medium">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <Label>Preferences</Label>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="notifications" />
                          <Label htmlFor="notifications">
                            Email notifications
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="marketing" />
                          <Label htmlFor="marketing">Marketing emails</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="updates" defaultChecked />
                          <Label htmlFor="updates">Product updates</Label>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label>Account Type</Label>
                      <RadioGroup defaultValue="personal">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="personal" id="personal" />
                          <Label htmlFor="personal">Personal</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="business" id="business" />
                          <Label htmlFor="business">Business</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="enterprise" id="enterprise" />
                          <Label htmlFor="enterprise">Enterprise</Label>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label>Volume</Label>
                      <span className="text-sm text-muted-foreground">75%</span>
                    </div>
                    <Slider defaultValue={[75]} max={100} step={1} />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="dark-mode">Dark Mode</Label>
                    <Switch id="dark-mode" />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="feedback" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Feedback & Status</CardTitle>
                  <CardDescription>
                    Components for displaying status and feedback
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Badges</Label>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="default">Default</Badge>
                      <Badge variant="secondary">Secondary</Badge>
                      <Badge variant="outline">Outline</Badge>
                      <Badge variant="success">Success</Badge>
                      <Badge variant="warning">Warning</Badge>
                      <Badge variant="info">Info</Badge>
                      <Badge variant="danger">Danger</Badge>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Alerts</Label>
                    <div className="space-y-3">
                      <Alert>
                        <Info className="size-4" />
                        <AlertTitle>Information</AlertTitle>
                        <AlertDescription>
                          This is an informational alert with some details.
                        </AlertDescription>
                      </Alert>

                      <Alert variant="success">
                        <CheckCircle className="size-4" />
                        <AlertTitle>Success</AlertTitle>
                        <AlertDescription>
                          Your changes have been saved successfully.
                        </AlertDescription>
                      </Alert>

                      <Alert variant="warning">
                        <AlertTriangle className="size-4" />
                        <AlertTitle>Warning</AlertTitle>
                        <AlertDescription>
                          Please review your settings before proceeding.
                        </AlertDescription>
                      </Alert>

                      <Alert variant="danger">
                        <AlertCircle className="size-4" />
                        <AlertTitle>Error</AlertTitle>
                        <AlertDescription>
                          Something went wrong. Please try again.
                        </AlertDescription>
                      </Alert>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Progress</Label>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Upload Progress</span>
                          <span>45%</span>
                        </div>
                        <Progress value={45} />
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Installation</span>
                          <span>78%</span>
                        </div>
                        <Progress value={78} />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">
                      Loading States
                    </Label>
                    <div className="space-y-3">
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="data" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Data Display</CardTitle>
                  <CardDescription>
                    Components for displaying and organizing data
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Avatars</Label>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Avatar size="sm">
                          <AvatarImage
                            src="https://github.com/shadcn.png"
                            alt="User"
                          />
                          <AvatarFallback>CN</AvatarFallback>
                        </Avatar>
                        <span className="text-sm">Small</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Avatar size="md">
                          <AvatarImage
                            src="https://github.com/vercel.png"
                            alt="Vercel"
                          />
                          <AvatarFallback>V</AvatarFallback>
                        </Avatar>
                        <span className="text-sm">Medium</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Avatar size="lg">
                          <AvatarFallback>LG</AvatarFallback>
                        </Avatar>
                        <span className="text-sm">Large</span>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">User Profiles</Label>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Avatar size="sm">
                            <AvatarImage
                              src="https://github.com/shadcn.png"
                              alt="John Doe"
                            />
                            <AvatarFallback>JD</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">John Doe</div>
                            <div className="text-sm text-muted-foreground">
                              <EMAIL>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="success">Active</Badge>
                          <Button size="sm" variant="outline">
                            View
                          </Button>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Avatar size="sm">
                            <AvatarImage
                              src="https://github.com/vercel.png"
                              alt="Jane Smith"
                            />
                            <AvatarFallback>JS</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">Jane Smith</div>
                            <div className="text-sm text-muted-foreground">
                              <EMAIL>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="warning">Pending</Badge>
                          <Button size="sm" variant="outline">
                            View
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">
                      Statistics Cards
                    </Label>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Total Users
                              </p>
                              <p className="text-2xl font-bold">1,234</p>
                            </div>
                            <User className="size-8 text-muted-foreground" />
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Messages
                              </p>
                              <p className="text-2xl font-bold">567</p>
                            </div>
                            <Mail className="size-8 text-muted-foreground" />
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Notifications
                              </p>
                              <p className="text-2xl font-bold">89</p>
                            </div>
                            <Bell className="size-8 text-muted-foreground" />
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="layout" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Layout & Structure</CardTitle>
                  <CardDescription>
                    Components for organizing content and layout
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Cards</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Project Alpha</CardTitle>
                          <CardDescription>
                            A revolutionary new project
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground">
                            This project aims to transform how we think about
                            user interfaces.
                          </p>
                        </CardContent>
                        <CardFooter>
                          <Button className="w-full">Learn More</Button>
                        </CardFooter>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle>Analytics Dashboard</CardTitle>
                          <CardDescription>
                            Real-time insights and metrics
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>Conversion Rate</span>
                              <span className="font-medium">12.5%</span>
                            </div>
                            <Progress value={12.5} />
                          </div>
                        </CardContent>
                        <CardFooter>
                          <Button variant="outline" className="w-full">
                            View Details
                          </Button>
                        </CardFooter>
                      </Card>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">
                      Content Organization
                    </Label>
                    <Card>
                      <CardContent className="p-6">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <h3 className="font-medium">Team Settings</h3>
                            <Badge variant="info">New</Badge>
                          </div>

                          <Separator />

                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">
                                  Team Notifications
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  Get notified about team activities
                                </p>
                              </div>
                              <Switch defaultChecked />
                            </div>

                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">Auto-save</p>
                                <p className="text-sm text-muted-foreground">
                                  Automatically save your work
                                </p>
                              </div>
                              <Switch />
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ThemeProvider>
      <App />
    </ThemeProvider>
  </React.StrictMode>
);
