import * as React from "react";

import { ThemeToggle } from "./theme-toggle";
import { cn } from "../utils/cn";

interface HeaderProps {
  /**
   * The main title/brand name
   */
  title?: React.ReactNode;
  /**
   * Navigation items to display
   */
  navigation?: React.ReactNode;
  /**
   * Actions to display on the right side (before theme toggle)
   */
  actions?: React.ReactNode;
  /**
   * Whether to show the theme toggle
   */
  showThemeToggle?: boolean;
  /**
   * Theme toggle configuration
   */
  themeToggleProps?: {
    modeOnly?: boolean;
    variantOnly?: boolean;
    showLabels?: boolean;
    size?: "sm" | "default" | "lg";
    variant?: "default" | "outline" | "ghost" | "secondary";
  };
  /**
   * Additional CSS classes for the header container
   */
  className?: string;
  /**
   * Whether to add a border at the bottom
   */
  bordered?: boolean;
  /**
   * Whether to make the header sticky
   */
  sticky?: boolean;
  /**
   * Header size variant
   */
  size?: "sm" | "default" | "lg";
}

/**
 * Professional header component with theme toggle and customizable content
 */
export function Header({
  title,
  navigation,
  actions,
  showThemeToggle = true,
  themeToggleProps = {},
  className,
  bordered = true,
  sticky = false,
  size = "default",
}: HeaderProps) {
  const sizeClasses = {
    sm: "h-12 px-4",
    default: "h-16 px-6",
    lg: "h-20 px-8",
  };

  return (
    <header
      className={cn(
        "flex items-center justify-between bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
        sizeClasses[size],
        bordered && "border-b border-border",
        sticky && "sticky top-0 z-50",
        className,
      )}
    >
      {/* Left section: Title and Navigation */}
      <div className="flex items-center gap-6">
        {title && (
          <div className="flex items-center">
            {typeof title === "string" ? (
              <h1 className="text-lg font-semibold text-foreground">{title}</h1>
            ) : (
              title
            )}
          </div>
        )}
        
        {navigation && (
          <nav className="hidden md:flex items-center gap-4">
            {navigation}
          </nav>
        )}
      </div>

      {/* Right section: Actions and Theme Toggle */}
      <div className="flex items-center gap-2">
        {actions && (
          <div className="flex items-center gap-2">
            {actions}
          </div>
        )}
        
        {showThemeToggle && (
          <ThemeToggle
            size={size === "sm" ? "sm" : "default"}
            {...themeToggleProps}
          />
        )}
      </div>
    </header>
  );
}

/**
 * Header navigation link component for consistent styling
 */
export function HeaderNavLink({
  href,
  children,
  active = false,
  className,
  ...props
}: React.ComponentProps<"a"> & {
  active?: boolean;
}) {
  return (
    <a
      href={href}
      className={cn(
        "text-sm font-medium transition-colors hover:text-foreground/80",
        active ? "text-foreground" : "text-foreground/60",
        className,
      )}
      {...props}
    >
      {children}
    </a>
  );
}

/**
 * Header brand/logo component for consistent styling
 */
export function HeaderBrand({
  children,
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      className={cn("flex items-center gap-2 font-bold text-lg", className)}
      {...props}
    >
      {children}
    </div>
  );
}
